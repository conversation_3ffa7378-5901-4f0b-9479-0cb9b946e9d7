#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库中的编码问题
"""

import logging
import sys
import unicodedata
import re
from typing import List, Tuple
from database_handler import DatabaseHandler
from config_loader import ConfigLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('encoding_fix.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)


class EncodingFixer:
    """编码问题修复器"""
    
    def __init__(self):
        """初始化修复器"""
        self.config_loader = ConfigLoader()
        self.db_handler = DatabaseHandler(self.config_loader)
        
    def detect_encoding_issues(self) -> List[Tuple[int, str, str]]:
        """
        检测数据库中的编码问题
        
        Returns:
            包含问题记录的列表 [(id, original_filename, document_content), ...]
        """
        problematic_records = []
        
        try:
            if not self.db_handler.connect():
                logging.error("无法连接到数据库")
                return problematic_records
                
            # 查询所有文档记录
            sql = "SELECT id, original_filename, document_content FROM documents"
            self.db_handler.cursor.execute(sql)
            records = self.db_handler.cursor.fetchall()
            
            logging.info(f"检查 {len(records)} 条记录的编码问题...")
            
            for record in records:
                doc_id, filename, content = record
                
                # 检查文件名编码问题
                if self._has_encoding_issues(filename):
                    logging.warning(f"文件名编码问题: ID={doc_id}, 文件名={repr(filename)}")
                    problematic_records.append((doc_id, filename, content))
                    continue
                    
                # 检查内容编码问题
                if content and self._has_encoding_issues(content):
                    logging.warning(f"内容编码问题: ID={doc_id}, 文件名={filename}")
                    problematic_records.append((doc_id, filename, content))
                    
        except Exception as e:
            logging.error(f"检测编码问题时出错: {e}")
        finally:
            self.db_handler.disconnect()
            
        return problematic_records
    
    def _has_encoding_issues(self, text: str) -> bool:
        """
        检查文本是否有编码问题
        
        Args:
            text: 要检查的文本
            
        Returns:
            是否有编码问题
        """
        if not text:
            return False
            
        try:
            # 检查是否包含常见的乱码模式
            garbled_patterns = [
                r'[?]{2,}',  # 连续问号
                r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]',  # 控制字符
                r'[^\x00-\x7F\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\u30a0-\u30ff\u0100-\u017f\u0180-\u024f\u1e00-\u1eff\u2000-\u206f\u20a0-\u20cf\u2100-\u214f\u2190-\u21ff\u2200-\u22ff\u2300-\u23ff\u2400-\u243f\u2440-\u245f\u2460-\u24ff\u2500-\u257f\u2580-\u259f\u25a0-\u25ff\u2600-\u26ff\u2700-\u27bf\u27c0-\u27ef\u27f0-\u27ff\u2800-\u28ff\u2900-\u297f\u2980-\u29ff\u2a00-\u2aff\u2b00-\u2bff\uff00-\uffef\s\n\r\t.,;:!?()[\]{}"\'`~@#$%^&*+=<>|\\/-_]{10,}',  # 异常字符序列
            ]
            
            for pattern in garbled_patterns:
                if re.search(pattern, text):
                    return True
                    
            # 检查Unicode规范化问题
            try:
                normalized = unicodedata.normalize('NFC', text)
                if normalized != text:
                    return True
            except:
                return True
                
            return False
            
        except Exception:
            return True
    
    def fix_encoding_issues(self, dry_run: bool = True) -> int:
        """
        修复编码问题
        
        Args:
            dry_run: 是否为试运行模式
            
        Returns:
            修复的记录数量
        """
        fixed_count = 0
        
        try:
            if not self.db_handler.connect():
                logging.error("无法连接到数据库")
                return 0
                
            # 获取有问题的记录
            problematic_records = self.detect_encoding_issues()
            
            if not problematic_records:
                logging.info("未发现编码问题")
                return 0
                
            logging.info(f"发现 {len(problematic_records)} 条有编码问题的记录")
            
            for doc_id, filename, content in problematic_records:
                try:
                    # 修复文件名
                    fixed_filename = self.db_handler.normalize_text(filename)
                    
                    # 修复内容
                    fixed_content = self.db_handler.normalize_text(content) if content else ""
                    
                    if dry_run:
                        logging.info(f"[试运行] 将修复记录 ID={doc_id}")
                        logging.info(f"  原文件名: {repr(filename)}")
                        logging.info(f"  新文件名: {repr(fixed_filename)}")
                        logging.info(f"  内容长度: {len(content) if content else 0} -> {len(fixed_content)}")
                    else:
                        # 更新数据库记录
                        update_sql = """
                        UPDATE documents 
                        SET original_filename = ?, document_content = ?
                        WHERE id = ?
                        """
                        
                        self.db_handler.cursor.execute(update_sql, (fixed_filename, fixed_content, doc_id))
                        self.db_handler.connection.commit()
                        
                        logging.info(f"已修复记录 ID={doc_id}, 文件名={fixed_filename}")
                    
                    fixed_count += 1
                    
                except Exception as e:
                    logging.error(f"修复记录 ID={doc_id} 时出错: {e}")
                    
        except Exception as e:
            logging.error(f"修复编码问题时出错: {e}")
        finally:
            self.db_handler.disconnect()
            
        return fixed_count
    
    def backup_database(self) -> bool:
        """
        备份数据库（建议在修复前执行）
        
        Returns:
            备份是否成功
        """
        try:
            if not self.db_handler.connect():
                logging.error("无法连接到数据库")
                return False
                
            # 创建备份表
            backup_sql = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='documents_backup' AND xtype='U')
            SELECT * INTO documents_backup FROM documents
            """
            
            self.db_handler.cursor.execute(backup_sql)
            self.db_handler.connection.commit()
            
            logging.info("数据库备份完成: documents_backup")
            return True
            
        except Exception as e:
            logging.error(f"备份数据库失败: {e}")
            return False
        finally:
            self.db_handler.disconnect()


def main():
    """主函数"""
    fixer = EncodingFixer()
    
    print("=== 数据库编码问题修复工具 ===")
    print()
    
    # 检测编码问题
    print("1. 检测编码问题...")
    problematic_records = fixer.detect_encoding_issues()
    
    if not problematic_records:
        print("✅ 未发现编码问题")
        return
        
    print(f"⚠️  发现 {len(problematic_records)} 条有编码问题的记录")
    print()
    
    # 询问是否继续
    response = input("是否要修复这些问题？(y/N): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return
        
    # 备份数据库
    print("2. 备份数据库...")
    if fixer.backup_database():
        print("✅ 数据库备份完成")
    else:
        print("❌ 数据库备份失败")
        response = input("是否继续修复？(y/N): ").strip().lower()
        if response != 'y':
            print("操作已取消")
            return
    
    # 试运行
    print("3. 试运行修复...")
    fixer.fix_encoding_issues(dry_run=True)
    print()
    
    # 确认修复
    response = input("确认执行修复？(y/N): ").strip().lower()
    if response != 'y':
        print("操作已取消")
        return
        
    # 执行修复
    print("4. 执行修复...")
    fixed_count = fixer.fix_encoding_issues(dry_run=False)
    print(f"✅ 修复完成，共修复 {fixed_count} 条记录")


if __name__ == "__main__":
    main()
