#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的OLE文件处理器
专门处理有问题的OLE文件格式
"""

import os
import logging
import warnings
from typing import Optional, Tuple, Dict, Any

try:
    import olefile
except ImportError:
    olefile = None

try:
    import chardet
except ImportError:
    chardet = None


class EnhancedOLEHandler:
    """增强的OLE文件处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.logger = logging.getLogger(__name__)
        
    def is_ole_file_safe(self, file_path: str) -> bool:
        """
        安全地检查是否为OLE文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为OLE文件
        """
        if not olefile:
            return False
            
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                return olefile.isOleFile(file_path)
        except Exception as e:
            self.logger.debug(f"检查OLE文件失败 {file_path}: {e}")
            return False
            
    def extract_text_from_ole_stream(self, ole, stream_name: str) -> str:
        """
        从OLE流中提取文本
        
        Args:
            ole: OLE文件对象
            stream_name: 流名称
            
        Returns:
            提取的文本内容
        """
        try:
            with ole.open(stream_name) as stream:
                data = stream.read()
                
                # 尝试多种编码方式提取文本
                text_candidates = []
                
                # 方法1: 尝试常见编码
                encodings = ['utf-16le', 'utf-8', 'gbk', 'cp1252', 'latin1']
                for encoding in encodings:
                    try:
                        decoded_text = data.decode(encoding, errors='ignore')
                        # 提取可打印字符
                        printable_chars = ''.join(char for char in decoded_text 
                                                 if char.isprintable() and ord(char) > 31)
                        if len(printable_chars) > 10:  # 至少要有一些有意义的内容
                            text_candidates.append(printable_chars)
                    except:
                        continue
                
                # 方法2: 字节级提取
                if not text_candidates:
                    byte_text = []
                    for i in range(len(data)):
                        try:
                            if 32 <= data[i] <= 126:  # ASCII可打印字符
                                byte_text.append(chr(data[i]))
                            elif data[i] == 0:  # 跳过空字节
                                continue
                        except:
                            continue
                    
                    if byte_text:
                        text_candidates.append(''.join(byte_text))
                
                # 返回最长的候选文本
                if text_candidates:
                    best_text = max(text_candidates, key=len)
                    return ' '.join(best_text.split())  # 清理空白字符
                    
        except Exception as e:
            self.logger.debug(f"从流 {stream_name} 提取文本失败: {e}")
            
        return ""
        
    def extract_doc_content(self, file_path: str) -> Tuple[str, Optional[str], Optional[str]]:
        """
        提取DOC文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            (文档内容, 创建者, 创建时间)
        """
        if not self.is_ole_file_safe(file_path):
            return "", None, None
            
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                ole = olefile.OleFileIO(file_path)
                
            content = ""
            creator = None
            create_time = None
            
            try:
                # 列出所有流
                streams = ole.listdir()
                self.logger.debug(f"DOC文件中的流: {streams}")
                
                # 尝试提取WordDocument流
                if 'WordDocument' in streams:
                    content = self.extract_text_from_ole_stream(ole, 'WordDocument')
                    
                # 如果WordDocument失败，尝试其他可能的流
                if not content:
                    for stream in streams:
                        if isinstance(stream, str) and 'word' in stream.lower():
                            text = self.extract_text_from_ole_stream(ole, stream)
                            if len(text) > len(content):
                                content = text
                                
                # 尝试提取文档属性
                try:
                    if '\x05SummaryInformation' in streams:
                        # 这里可以添加更详细的属性提取逻辑
                        pass
                except Exception as e:
                    self.logger.debug(f"提取文档属性失败: {e}")
                    
            finally:
                ole.close()
                
            return content, creator, create_time
            
        except Exception as e:
            self.logger.warning(f"使用增强OLE处理器提取DOC内容失败 {file_path}: {e}")
            return "", None, None
            
    def extract_xls_content(self, file_path: str) -> Tuple[str, Optional[str], Optional[str]]:
        """
        提取XLS文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            (文档内容, 创建者, 创建时间)
        """
        if not self.is_ole_file_safe(file_path):
            return "", None, None
            
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                ole = olefile.OleFileIO(file_path)
                
            content_parts = []
            creator = None
            create_time = None
            
            try:
                # 列出所有流
                streams = ole.listdir()
                self.logger.debug(f"XLS文件中的流: {streams}")
                
                # 尝试提取Workbook流
                if 'Workbook' in streams:
                    text = self.extract_text_from_ole_stream(ole, 'Workbook')
                    if text:
                        content_parts.append(text)
                        
                # 尝试其他可能包含数据的流
                for stream in streams:
                    if isinstance(stream, str) and any(keyword in stream.lower() 
                                                     for keyword in ['book', 'sheet', 'data']):
                        text = self.extract_text_from_ole_stream(ole, stream)
                        if text and text not in content_parts:
                            content_parts.append(text)
                            
            finally:
                ole.close()
                
            # 组合内容
            final_content = ' '.join(content_parts)
            final_content = ' '.join(final_content.split())  # 清理空白字符
            
            return final_content, creator, create_time
            
        except Exception as e:
            self.logger.warning(f"使用增强OLE处理器提取XLS内容失败 {file_path}: {e}")
            return "", None, None
            
    def extract_ole_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        提取OLE文件的基本信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        info = {
            'is_ole': False,
            'streams': [],
            'file_size': 0,
            'error': None
        }
        
        try:
            info['file_size'] = os.path.getsize(file_path)
            
            if self.is_ole_file_safe(file_path):
                info['is_ole'] = True
                
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    ole = olefile.OleFileIO(file_path)
                    
                try:
                    info['streams'] = ole.listdir()
                finally:
                    ole.close()
                    
        except Exception as e:
            info['error'] = str(e)
            self.logger.debug(f"获取OLE文件信息失败 {file_path}: {e}")
            
        return info
