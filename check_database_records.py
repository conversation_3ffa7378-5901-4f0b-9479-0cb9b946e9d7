#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看数据库记录
"""

from config_loader import Config<PERSON>oader
from database_handler import DatabaseHandler

def check_records():
    """查看数据库记录"""
    config_loader = ConfigLoader()
    db_handler = DatabaseHandler(config_loader)

    try:
        if db_handler.connect():
            print("数据库连接成功")

            cursor = db_handler.cursor

            # 查看记录总数
            cursor.execute("SELECT COUNT(*) FROM documents")
            count = cursor.fetchone()[0]
            print(f"\n数据库中共有 {count} 条记录")

            if count > 0:
                # 查看最近的记录
                cursor.execute("""
                    SELECT TOP 10
                        original_filename,
                        saved_filename,
                        document_creator,
                        document_create_time,
                        upload_time,
                        CAST(LEFT(CAST(document_content AS NVARCHAR(MAX)), 100) AS NVARCHAR(100)) as content_preview
                    FROM documents
                    ORDER BY upload_time DESC
                """)

                records = cursor.fetchall()
                print("\n最近的记录:")
                print("-" * 100)
                print(f"{'原始文件名':<20} {'保存文件名':<35} {'创建者':<10} {'内容预览':<30}")
                print("-" * 100)

                for record in records:
                    original_name = record[0] or "N/A"
                    saved_name = record[1] or "N/A"
                    creator = record[2] or "N/A"
                    content = (record[5] or "").replace('\n', ' ').replace('\r', ' ')[:30]

                    print(f"{original_name:<20} {saved_name:<35} {creator:<10} {content:<30}")

        else:
            print("数据库连接失败")

    except Exception as e:
        print(f"查看记录时出错: {e}")
    finally:
        db_handler.disconnect()

if __name__ == "__main__":
    check_records()
