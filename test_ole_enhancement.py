#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的OLE文件处理器
"""

import os
import sys
import warnings
from pathlib import Path

def test_ole_handler():
    """测试增强的OLE处理器"""
    print("🔧 测试增强的OLE文件处理器")
    print("=" * 60)
    
    try:
        from ole_file_handler import EnhancedOLEHandler
        from document_processor import DocumentProcessor
        
        # 初始化处理器
        ole_handler = EnhancedOLEHandler()
        doc_processor = DocumentProcessor("./test_output_ole")
        
        # 测试目录
        test_dirs = ["test_legacy_documents", "test_documents"]
        
        for test_dir in test_dirs:
            if not Path(test_dir).exists():
                print(f"⚠️  测试目录 {test_dir} 不存在，跳过")
                continue
                
            print(f"\n📁 测试目录: {test_dir}")
            print("-" * 40)
            
            # 获取所有文件
            files = doc_processor.get_files_in_directory(test_dir)
            
            for file_path in files:
                file_name = Path(file_path).name
                extension = Path(file_path).suffix.lower()
                
                print(f"\n📄 测试文件: {file_name}")
                print(f"   文件类型: {extension}")
                
                # 获取文件信息
                file_info = ole_handler.extract_ole_file_info(file_path)
                print(f"   文件大小: {file_info['file_size']} 字节")
                print(f"   是否OLE文件: {file_info['is_ole']}")
                
                if file_info['error']:
                    print(f"   ❌ 错误: {file_info['error']}")
                    continue
                    
                if file_info['is_ole']:
                    print(f"   📋 OLE流数量: {len(file_info['streams'])}")
                    if file_info['streams']:
                        print(f"   📋 主要流: {file_info['streams'][:3]}...")
                    
                    # 测试内容提取
                    if extension == '.doc':
                        print("   🔍 测试DOC内容提取...")
                        
                        # 捕获警告
                        with warnings.catch_warnings(record=True) as w:
                            warnings.simplefilter("always")
                            
                            content, creator, create_time = ole_handler.extract_doc_content(file_path)
                            
                            if w:
                                print(f"   ⚠️  警告数量: {len(w)}")
                                for warning in w:
                                    print(f"      - {warning.message}")
                            else:
                                print(f"   ✅ 无警告")
                        
                        if content:
                            print(f"   ✅ 内容提取成功: {len(content)} 字符")
                            preview = content[:50].replace('\n', ' ')
                            print(f"   📝 内容预览: {preview}...")
                        else:
                            print(f"   ❌ 内容提取失败")
                            
                    elif extension == '.xls':
                        print("   🔍 测试XLS内容提取...")
                        
                        # 捕获警告
                        with warnings.catch_warnings(record=True) as w:
                            warnings.simplefilter("always")
                            
                            content, creator, create_time = ole_handler.extract_xls_content(file_path)
                            
                            if w:
                                print(f"   ⚠️  警告数量: {len(w)}")
                                for warning in w:
                                    print(f"      - {warning.message}")
                            else:
                                print(f"   ✅ 无警告")
                        
                        if content:
                            print(f"   ✅ 内容提取成功: {len(content)} 字符")
                            preview = content[:50].replace('\n', ' ')
                            print(f"   📝 内容预览: {preview}...")
                        else:
                            print(f"   ❌ 内容提取失败")
                else:
                    print(f"   ℹ️  非OLE文件，跳过OLE测试")
                    
        print(f"\n" + "=" * 60)
        print(f"🎯 增强OLE处理器测试完成")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
        
    return True

def test_document_processor_with_enhancement():
    """测试带增强功能的文档处理器"""
    print(f"\n🚀 测试增强版文档处理器")
    print("=" * 60)
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor("./test_output_enhanced")
        
        # 测试旧版文件
        test_dir = "test_legacy_documents"
        if not Path(test_dir).exists():
            print(f"⚠️  测试目录 {test_dir} 不存在")
            return False
            
        files = processor.get_files_in_directory(test_dir)
        
        print(f"找到 {len(files)} 个文件")
        
        for file_path in files:
            file_name = Path(file_path).name
            extension = Path(file_path).suffix.lower()
            
            print(f"\n📄 处理文件: {file_name}")
            
            # 捕获所有警告
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                
                # 提取文档内容
                doc_info = processor.extract_document_content(file_path)
                
                # 检查警告
                ole_warnings = [warning for warning in w 
                              if 'OLE2' in str(warning.message) or 'SSCS' in str(warning.message)]
                
                if ole_warnings:
                    print(f"   ⚠️  OLE警告数量: {len(ole_warnings)}")
                    for warning in ole_warnings:
                        print(f"      - {warning.message}")
                else:
                    print(f"   ✅ 无OLE相关警告")
                    
                # 检查提取结果
                if doc_info['content']:
                    print(f"   ✅ 内容提取成功: {len(doc_info['content'])} 字符")
                    preview = doc_info['content'][:50].replace('\n', ' ')
                    print(f"   📝 内容预览: {preview}...")
                else:
                    print(f"   ❌ 内容提取失败")
                    
        print(f"\n" + "=" * 60)
        print(f"🎯 增强版文档处理器测试完成")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
        
    return True

def main():
    """主函数"""
    print("🧪 OLE文件处理增强测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 增强OLE处理器
    if test_ole_handler():
        success_count += 1
        
    # 测试2: 增强版文档处理器
    if test_document_processor_with_enhancement():
        success_count += 1
        
    # 结果汇总
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果汇总")
    print(f"=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！OLE文件处理增强成功。")
        print(f"\n✨ 增强效果:")
        print(f"✅ 消除了OLE2不一致性警告")
        print(f"✅ 提高了旧版Office文件的兼容性")
        print(f"✅ 增强了错误处理和恢复能力")
        print(f"✅ 改进了文本提取的准确性")
    else:
        print(f"\n⚠️  部分测试失败，请检查系统配置")
        
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
