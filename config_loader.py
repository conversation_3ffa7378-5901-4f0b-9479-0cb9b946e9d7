#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件加载模块
"""

import json
import os
from typing import Dict, Any


class ConfigLoader:
    """配置文件加载器"""

    def __init__(self, config_file: str = "nbfwq.json"):
        """
        初始化配置加载器

        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = None

    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件

        Returns:
            配置字典

        Raises:
            FileNotFoundError: 配置文件不存在
            json.JSONDecodeError: 配置文件格式错误
        """
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            return self.config
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"配置文件格式错误: {e}")

    def get_connection_string(self) -> str:
        """
        获取数据库连接字符串

        Returns:
            数据库连接字符串
        """
        if not self.config:
            self.load_config()

        conn_str = (
            f"DRIVER={self.config['driver']};"
            f"SERVER={self.config['server']};"
            f"DATABASE={self.config['database']};"
            f"UID={self.config['uid']};"
            f"PWD={self.config['pwd']};"
            f"Encrypt={self.config['encrypt']};"
            f"TrustServerCertificate={self.config['trustServerCertificate']};"
            f"CharacterSet=UTF-8;"
            f"ApplicationIntent=ReadWrite;"
        )
        return conn_str
