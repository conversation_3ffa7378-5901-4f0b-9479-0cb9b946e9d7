#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试哈希优化后的处理流程
"""

import os
import sys
import time
import logging
from pathlib import Path
from document_manager import DocumentManager

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('hash_optimization_test.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_hash_optimization():
    """测试哈希优化效果"""
    print("🚀 测试哈希优化后的处理流程")
    print("=" * 60)
    
    # 设置测试日志
    setup_test_logging()
    
    # 测试目录
    test_dirs = ["test_documents", "test_legacy_documents"]
    
    for test_dir in test_dirs:
        if not Path(test_dir).exists():
            print(f"⚠️  测试目录 {test_dir} 不存在，跳过")
            continue
            
        print(f"\n📁 测试目录: {test_dir}")
        print("-" * 40)
        
        # 创建文档管理器
        manager = DocumentManager(test_dir, "./test_output_hash_opt")
        
        # 第一次处理 - 新文件
        print(f"\n🔄 第一次处理（新文件）:")
        start_time = time.time()
        
        if manager.initialize_system():
            manager.process_all_files()
            first_run_time = time.time() - start_time
            first_stats = manager.stats.copy()
            
            print(f"⏱️  处理时间: {first_run_time:.2f} 秒")
            print(f"📊 统计信息:")
            print(f"   总文件数: {first_stats['total_files']}")
            print(f"   成功处理: {first_stats['processed_files']}")
            print(f"   重复跳过: {first_stats['duplicate_files']}")
            print(f"   处理失败: {first_stats['failed_files']}")
        else:
            print("❌ 系统初始化失败")
            continue
            
        manager.cleanup()
        
        # 第二次处理 - 重复文件（测试哈希优化效果）
        print(f"\n🔄 第二次处理（重复文件，测试哈希优化）:")
        manager = DocumentManager(test_dir, "./test_output_hash_opt")
        
        start_time = time.time()
        
        if manager.initialize_system():
            manager.process_all_files()
            second_run_time = time.time() - start_time
            second_stats = manager.stats.copy()
            
            print(f"⏱️  处理时间: {second_run_time:.2f} 秒")
            print(f"📊 统计信息:")
            print(f"   总文件数: {second_stats['total_files']}")
            print(f"   成功处理: {second_stats['processed_files']}")
            print(f"   重复跳过: {second_stats['duplicate_files']}")
            print(f"   处理失败: {second_stats['failed_files']}")
            
            # 性能对比
            print(f"\n📈 性能对比:")
            print(f"   第一次处理时间: {first_run_time:.2f} 秒")
            print(f"   第二次处理时间: {second_run_time:.2f} 秒")
            if first_run_time > 0:
                speedup = first_run_time / second_run_time
                print(f"   速度提升: {speedup:.2f}x")
                print(f"   时间节省: {((first_run_time - second_run_time) / first_run_time * 100):.1f}%")
                
            # 验证重复检测
            if second_stats['duplicate_files'] == first_stats['total_files']:
                print(f"   ✅ 重复检测正确: 所有文件都被识别为重复")
            else:
                print(f"   ❌ 重复检测异常")
                
        else:
            print("❌ 系统初始化失败")
            
        manager.cleanup()

def test_processing_flow():
    """测试处理流程的详细步骤"""
    print(f"\n🔍 测试处理流程详细步骤")
    print("=" * 60)
    
    # 使用一个测试文件
    test_files = []
    for test_dir in ["test_documents", "test_legacy_documents"]:
        if Path(test_dir).exists():
            files = list(Path(test_dir).glob("*.*"))
            if files:
                test_files.append(str(files[0]))
                break
                
    if not test_files:
        print("❌ 未找到测试文件")
        return False
        
    test_file = test_files[0]
    print(f"📄 测试文件: {Path(test_file).name}")
    
    # 创建文档管理器
    manager = DocumentManager(".", "./test_output_flow")
    
    if not manager.initialize_system():
        print("❌ 系统初始化失败")
        return False
        
    try:
        # 手动测试处理流程
        print(f"\n🔄 处理流程步骤:")
        
        # 步骤1: 计算哈希值
        print(f"1️⃣ 计算文件哈希值...")
        start_time = time.time()
        file_hash = manager.doc_processor.calculate_file_hash(test_file)
        hash_time = time.time() - start_time
        print(f"   ✅ 哈希值: {file_hash[:16]}...")
        print(f"   ⏱️  耗时: {hash_time:.4f} 秒")
        
        # 步骤2: 检查重复
        print(f"2️⃣ 检查文档是否已存在...")
        start_time = time.time()
        exists = manager.db_handler.document_exists(file_hash)
        check_time = time.time() - start_time
        print(f"   📋 结果: {'已存在' if exists else '不存在'}")
        print(f"   ⏱️  耗时: {check_time:.4f} 秒")
        
        if not exists:
            # 步骤3: 内容提取（仅在不存在时执行）
            print(f"3️⃣ 提取文档内容...")
            start_time = time.time()
            doc_info = manager.doc_processor.extract_document_content(test_file)
            extract_time = time.time() - start_time
            print(f"   ✅ 内容长度: {len(doc_info['content'])} 字符")
            print(f"   ⏱️  耗时: {extract_time:.4f} 秒")
            
            # 步骤4: 保存文件
            print(f"4️⃣ 保存文件...")
            start_time = time.time()
            new_filename = manager.doc_processor.save_document_with_uuid(test_file)
            save_time = time.time() - start_time
            print(f"   ✅ 新文件名: {new_filename}")
            print(f"   ⏱️  耗时: {save_time:.4f} 秒")
            
            total_time = hash_time + check_time + extract_time + save_time
        else:
            print(f"3️⃣ 跳过内容提取（文档已存在）")
            print(f"4️⃣ 跳过文件保存（文档已存在）")
            total_time = hash_time + check_time
            
        print(f"\n📊 时间分析:")
        print(f"   哈希计算: {hash_time:.4f} 秒 ({hash_time/total_time*100:.1f}%)")
        print(f"   重复检查: {check_time:.4f} 秒 ({check_time/total_time*100:.1f}%)")
        if not exists:
            print(f"   内容提取: {extract_time:.4f} 秒 ({extract_time/total_time*100:.1f}%)")
            print(f"   文件保存: {save_time:.4f} 秒 ({save_time/total_time*100:.1f}%)")
        print(f"   总计时间: {total_time:.4f} 秒")
        
        print(f"\n✨ 优化效果:")
        if exists:
            print(f"   ✅ 重复文件快速跳过，避免了耗时的内容提取")
            print(f"   ✅ 处理时间大幅缩短")
        else:
            print(f"   ✅ 新文件正常处理，流程优化")
            
    finally:
        manager.cleanup()
        
    return True

def main():
    """主函数"""
    print("🧪 哈希优化测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 哈希优化效果
    try:
        test_hash_optimization()
        success_count += 1
    except Exception as e:
        print(f"❌ 哈希优化测试失败: {e}")
        
    # 测试2: 处理流程分析
    try:
        if test_processing_flow():
            success_count += 1
    except Exception as e:
        print(f"❌ 处理流程测试失败: {e}")
        
    # 结果汇总
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果汇总")
    print(f"=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！哈希优化效果显著。")
        print(f"\n✨ 优化效果:")
        print(f"✅ 重复文件检测前置，避免不必要的内容提取")
        print(f"✅ 处理速度大幅提升，特别是重复文件场景")
        print(f"✅ 系统资源使用更加高效")
        print(f"✅ 用户体验显著改善")
    else:
        print(f"\n⚠️  部分测试失败，请检查系统配置")
        
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
