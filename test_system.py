#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
"""

import os
import sys
from pathlib import Path
from docx import Document
from openpyxl import Workbook
from pptx import Presentation
from pptx.util import Inches

def create_test_documents():
    """创建测试文档"""
    test_dir = Path("test_documents")
    test_dir.mkdir(exist_ok=True)

    print("正在创建测试文档...")

    # 创建Word文档
    doc = Document()
    doc.add_heading('测试文档标题', 0)
    doc.add_paragraph('这是一个测试段落，包含中文内容。')
    doc.add_paragraph('第二个段落，用于测试文档内容提取功能。')

    # 添加表格
    table = doc.add_table(rows=2, cols=2)
    table.cell(0, 0).text = '姓名'
    table.cell(0, 1).text = '年龄'
    table.cell(1, 0).text = '张三'
    table.cell(1, 1).text = '25'

    doc.save(test_dir / "test_document.docx")
    print("✓ 创建了 test_document.docx")

    # 创建Excel文档
    wb = Workbook()
    ws = wb.active
    ws.title = "测试工作表"

    # 添加数据
    ws['A1'] = '产品名称'
    ws['B1'] = '价格'
    ws['C1'] = '数量'
    ws['A2'] = '苹果'
    ws['B2'] = 5.5
    ws['C2'] = 100
    ws['A3'] = '香蕉'
    ws['B3'] = 3.2
    ws['C3'] = 80

    wb.save(test_dir / "test_spreadsheet.xlsx")
    print("✓ 创建了 test_spreadsheet.xlsx")

    # 创建PowerPoint文档
    prs = Presentation()

    # 添加标题幻灯片
    title_slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(title_slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    title.text = "测试演示文稿"
    subtitle.text = "这是一个测试用的PowerPoint文档"

    # 添加内容幻灯片
    bullet_slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(bullet_slide_layout)
    shapes = slide.shapes
    title_shape = shapes.title
    body_shape = shapes.placeholders[1]

    title_shape.text = '主要内容'
    tf = body_shape.text_frame
    tf.text = '第一个要点'

    p = tf.add_paragraph()
    p.text = '第二个要点'
    p.level = 0

    p = tf.add_paragraph()
    p.text = '第三个要点'
    p.level = 0

    prs.save(test_dir / "test_presentation.pptx")
    print("✓ 创建了 test_presentation.pptx")

    print(f"\n测试文档已创建在 {test_dir} 目录中")
    return str(test_dir)

def test_database_connection():
    """测试数据库连接"""
    print("\n正在测试数据库连接...")

    try:
        from config_loader import ConfigLoader
        from database_handler import DatabaseHandler

        config_loader = ConfigLoader()
        db_handler = DatabaseHandler(config_loader)

        if db_handler.connect():
            print("✓ 数据库连接成功")

            if db_handler.test_connection():
                print("✓ 数据库连接测试通过")

                if db_handler.create_documents_table():
                    print("✓ 文档表创建成功")
                    db_handler.disconnect()
                    return True
                else:
                    print("✗ 文档表创建失败")
            else:
                print("✗ 数据库连接测试失败")
        else:
            print("✗ 数据库连接失败")

        db_handler.disconnect()
        return False

    except Exception as e:
        print(f"✗ 数据库测试异常: {e}")
        return False

def test_document_processing():
    """测试文档处理功能"""
    print("\n正在测试文档处理功能...")

    try:
        from document_processor import DocumentProcessor

        processor = DocumentProcessor("./test_output")
        test_dir = Path("test_documents")

        # 测试文件扫描
        files = processor.get_files_in_directory(str(test_dir))
        print(f"✓ 找到 {len(files)} 个文档文件")

        # 测试文档内容提取
        for file_path in files:
            print(f"  测试文件: {Path(file_path).name}")

            # 计算哈希值
            file_hash = processor.calculate_file_hash(file_path)
            if file_hash:
                print(f"    ✓ 哈希值: {file_hash[:16]}...")
            else:
                print(f"    ✗ 哈希值计算失败")

            # 提取文档内容
            doc_info = processor.extract_document_content(file_path)
            if doc_info['content']:
                content_preview = doc_info['content'][:50].replace('\n', ' ')
                print(f"    ✓ 内容预览: {content_preview}...")
            else:
                print(f"    ✗ 内容提取失败")

        return True

    except Exception as e:
        print(f"✗ 文档处理测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("文档保存系统测试")
    print("=" * 50)

    # 创建测试文档
    test_dir = create_test_documents()

    # 测试数据库连接
    db_ok = test_database_connection()

    # 测试文档处理
    doc_ok = test_document_processing()

    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"数据库连接: {'✓ 通过' if db_ok else '✗ 失败'}")
    print(f"文档处理: {'✓ 通过' if doc_ok else '✗ 失败'}")

    if db_ok and doc_ok:
        print("\n🎉 所有测试通过！系统可以正常使用。")
        print(f"\n现在可以运行主程序:")
        print(f"python3 document_manager.py {test_dir}")
    else:
        print("\n❌ 部分测试失败，请检查配置和依赖。")

    print("=" * 50)

if __name__ == "__main__":
    main()
