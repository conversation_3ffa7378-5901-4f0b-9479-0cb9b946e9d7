#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专用XLS文件处理器
避免OLE2警告的专门实现
"""

import os
import logging
import warnings
import subprocess
import tempfile
from typing import Optional, Tuple, List
from pathlib import Path

try:
    import chardet
except ImportError:
    chardet = None


class XLSProcessor:
    """专用XLS文件处理器"""

    def __init__(self):
        """初始化处理器"""
        self.logger = logging.getLogger(__name__)

    def clean_and_encode_content(self, content: str) -> str:
        """
        清理和编码转换内容，确保UTF-16兼容

        Args:
            content: 原始内容

        Returns:
            清理后的UTF-16兼容内容
        """
        if not content:
            return ""

        try:
            # 1. 移除控制字符和不可打印字符
            cleaned_chars = []
            for char in content:
                # 保留可打印字符、中文字符、常用标点和空白字符
                if (char.isprintable() or
                    '\u4e00' <= char <= '\u9fff' or  # 中日韩统一表意文字
                    '\u3400' <= char <= '\u4dbf' or  # 中日韩统一表意文字扩展A
                    '\u20000' <= char <= '\u2a6df' or  # 中日韩统一表意文字扩展B
                    '\u3000' <= char <= '\u303f' or  # 中日韩符号和标点
                    '\uff00' <= char <= '\uffef' or  # 全角ASCII、全角标点
                    char in '\n\r\t '):  # 基本空白字符
                    cleaned_chars.append(char)

            content = ''.join(cleaned_chars)

            # 2. 确保UTF-16兼容性
            try:
                # 尝试编码为UTF-16然后解码，确保兼容性
                test_encoded = content.encode('utf-16le')
                # 验证解码是否正常
                test_decoded = test_encoded.decode('utf-16le')
                if test_decoded != content:
                    raise UnicodeEncodeError("utf-16le", content, 0, len(content), "编码验证失败")
            except UnicodeEncodeError as e:
                self.logger.debug(f"UTF-16编码失败，进行字符清理: {e}")
                # 逐字符检查和清理
                safe_chars = []
                for char in content:
                    try:
                        char.encode('utf-16le')
                        safe_chars.append(char)
                    except UnicodeEncodeError:
                        # 替换为安全字符
                        if '\u4e00' <= char <= '\u9fff':  # 中文字符
                            safe_chars.append('?')  # 用问号替代有问题的中文字符
                        # 其他字符直接忽略
                content = ''.join(safe_chars)

            # 3. 清理多余的空白字符
            lines = content.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                if line:  # 只保留非空行
                    cleaned_lines.append(line)

            # 4. 限制内容长度，避免过长
            final_content = '\n'.join(cleaned_lines)
            if len(final_content) > 10000:  # 限制为10KB
                final_content = final_content[:10000] + "...(内容已截断)"

            return final_content

        except Exception as e:
            self.logger.warning(f"内容清理失败: {e}")
            # 返回安全的ASCII内容
            safe_content = ''.join(char for char in content if ord(char) < 128)
            return safe_content[:1000] if safe_content else ""

    def extract_with_libreoffice_csv(self, file_path: str) -> str:
        """
        使用LibreOffice转换为CSV然后提取内容

        Args:
            file_path: 文件路径

        Returns:
            文档内容
        """
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # 转换为CSV文件
                cmd = [
                    'libreoffice',
                    '--headless',
                    '--convert-to', 'csv',
                    '--outdir', temp_dir,
                    file_path
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    env=dict(os.environ, HOME=temp_dir)
                )

                if result.returncode == 0:
                    # 查找生成的CSV文件
                    csv_files = list(Path(temp_dir).glob("*.csv"))
                    if csv_files:
                        content_parts = []

                        for csv_file in csv_files:
                            try:
                                # 尝试不同的编码读取CSV文件，优先中文编码
                                encodings = ['utf-8', 'gbk', 'cp936', 'utf-16', 'utf-16le', 'utf-16be', 'cp1252', 'latin1', 'big5']
                                csv_content = None
                                detected_encoding = None

                                # 首先尝试自动检测编码
                                if chardet:
                                    try:
                                        with open(csv_file, 'rb') as f:
                                            raw_data = f.read()
                                        detected = chardet.detect(raw_data)
                                        if detected['encoding'] and detected['confidence'] > 0.7:
                                            detected_encoding = detected['encoding']
                                            self.logger.debug(f"检测到编码: {detected_encoding} (置信度: {detected['confidence']})")
                                    except Exception as e:
                                        self.logger.debug(f"编码检测失败: {e}")

                                # 如果检测到编码，优先尝试
                                if detected_encoding:
                                    encodings.insert(0, detected_encoding)

                                for encoding in encodings:
                                    try:
                                        with open(csv_file, 'r', encoding=encoding, errors='ignore') as f:
                                            csv_content = f.read()
                                        self.logger.debug(f"成功使用编码 {encoding} 读取文件")
                                        break
                                    except (UnicodeDecodeError, UnicodeError):
                                        continue

                                if csv_content:
                                    # 处理CSV内容
                                    lines = csv_content.strip().split('\n')
                                    for line in lines:
                                        if line.strip():
                                            # 简单处理CSV格式
                                            cells = line.split(',')
                                            cleaned_cells = []
                                            for cell in cells:
                                                cell = cell.strip().strip('"')
                                                if cell:
                                                    cleaned_cells.append(cell)
                                            if cleaned_cells:
                                                content_parts.append(' | '.join(cleaned_cells))

                            except Exception as e:
                                self.logger.debug(f"读取CSV文件失败 {csv_file}: {e}")

                        # 清理和编码转换
                        raw_content = '\n'.join(content_parts)
                        return self.clean_and_encode_content(raw_content)
                else:
                    self.logger.warning(f"LibreOffice CSV转换失败: {result.stderr}")

        except subprocess.TimeoutExpired:
            self.logger.warning(f"LibreOffice CSV转换超时: {file_path}")
        except Exception as e:
            self.logger.warning(f"LibreOffice CSV转换异常 {file_path}: {e}")

        return ""

    def extract_with_strings(self, file_path: str) -> str:
        """
        使用strings命令提取可读文本

        Args:
            file_path: 文件路径

        Returns:
            文档内容
        """
        try:
            # 使用strings命令提取可读字符串
            result = subprocess.run(
                ['strings', '-n', '3', file_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                content_parts = []

                for line in lines:
                    line = line.strip()
                    # 过滤掉明显的非内容行
                    if (len(line) >= 3 and
                        not line.startswith('Microsoft') and
                        not line.startswith('Excel') and
                        not line.startswith('Calc') and
                        not all(c in '0123456789.-+' for c in line)):
                        content_parts.append(line)

                # 去重并限制数量
                unique_parts = list(dict.fromkeys(content_parts))[:100]
                raw_content = '\n'.join(unique_parts)
                return self.clean_and_encode_content(raw_content)
            else:
                self.logger.debug(f"strings命令失败: {result.stderr}")

        except subprocess.TimeoutExpired:
            self.logger.warning(f"strings命令超时: {file_path}")
        except FileNotFoundError:
            self.logger.debug("strings命令未找到，跳过")
        except Exception as e:
            self.logger.warning(f"strings提取异常 {file_path}: {e}")

        return ""

    def extract_with_hexdump(self, file_path: str) -> str:
        """
        使用hexdump提取文本内容，支持中文字符

        Args:
            file_path: 文件路径

        Returns:
            文档内容
        """
        try:
            # 读取文件的前64KB进行分析
            with open(file_path, 'rb') as f:
                data = f.read(65536)  # 64KB

            text_parts = []

            # 方法1: 尝试UTF-8解码
            try:
                utf8_text = data.decode('utf-8', errors='ignore')
                # 提取有意义的文本片段
                lines = utf8_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if (len(line) >= 2 and
                        not line.startswith('Microsoft') and
                        not line.startswith('Excel') and
                        not line.startswith('Calc') and
                        not line.isdigit() and
                        any(c.isalnum() or '\u4e00' <= c <= '\u9fff' for c in line)):
                        text_parts.append(line)
            except:
                pass

            # 方法2: 尝试GBK解码（中文编码）
            try:
                gbk_text = data.decode('gbk', errors='ignore')
                lines = gbk_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if (len(line) >= 2 and
                        not line.startswith('Microsoft') and
                        not line.startswith('Excel') and
                        not line.startswith('Calc') and
                        not line.isdigit() and
                        any(c.isalnum() or '\u4e00' <= c <= '\u9fff' for c in line)):
                        text_parts.append(line)
            except:
                pass

            # 方法3: ASCII字符提取（原有方法）
            current_string = []
            for byte in data:
                if 32 <= byte <= 126:  # ASCII可打印字符
                    current_string.append(chr(byte))
                else:
                    if len(current_string) >= 3:
                        text = ''.join(current_string)
                        if (not text.startswith('Microsoft') and
                            not text.startswith('Excel') and
                            not text.startswith('Calc') and
                            not text.isdigit() and
                            len(text.strip()) > 0):
                            text_parts.append(text.strip())
                    current_string = []

            # 处理最后的字符串
            if len(current_string) >= 3:
                text = ''.join(current_string)
                if (not text.startswith('Microsoft') and
                    not text.startswith('Excel') and
                    not text.startswith('Calc') and
                    not text.isdigit() and
                    len(text.strip()) > 0):
                    text_parts.append(text.strip())

            # 去重并限制数量
            unique_parts = list(dict.fromkeys(text_parts))[:50]
            raw_content = '\n'.join(unique_parts)
            return self.clean_and_encode_content(raw_content)

        except Exception as e:
            self.logger.warning(f"hexdump提取异常 {file_path}: {e}")

        return ""

    def extract_xls_content(self, file_path: str) -> Tuple[str, Optional[str], Optional[str]]:
        """
        提取XLS文件内容（无警告版本）

        Args:
            file_path: 文件路径

        Returns:
            (文档内容, 创建者, 创建时间)
        """
        content = ""
        creator = None
        create_time = None

        self.logger.info(f"使用专用XLS处理器处理: {file_path}")

        # 方法1: LibreOffice转CSV
        content = self.extract_with_libreoffice_csv(file_path)
        if content:
            self.logger.info(f"LibreOffice CSV方法成功提取内容: {len(content)} 字符")
            return content, creator, create_time

        # 方法2: strings命令
        self.logger.info(f"LibreOffice CSV失败，尝试strings命令: {file_path}")
        content = self.extract_with_strings(file_path)
        if content:
            self.logger.info(f"strings方法成功提取内容: {len(content)} 字符")
            return content, creator, create_time

        # 方法3: hexdump分析
        self.logger.info(f"strings失败，尝试hexdump分析: {file_path}")
        content = self.extract_with_hexdump(file_path)
        if content:
            self.logger.info(f"hexdump方法成功提取内容: {len(content)} 字符")
            return content, creator, create_time

        self.logger.warning(f"所有方法都无法提取XLS内容: {file_path}")
        return "", creator, create_time

    def is_valid_xls_file(self, file_path: str) -> bool:
        """
        检查是否为有效的XLS文件

        Args:
            file_path: 文件路径

        Returns:
            是否为有效的XLS文件
        """
        try:
            with open(file_path, 'rb') as f:
                header = f.read(8)

            # 检查OLE文件头
            ole_signature = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
            if header == ole_signature:
                return True

            # 检查BIFF格式
            biff_signatures = [
                b'\x09\x08',  # BIFF5
                b'\x05\x08',  # BIFF8
                b'\x00\x08',  # BIFF2
            ]

            for sig in biff_signatures:
                if header.startswith(sig):
                    return True

            return False

        except Exception as e:
            self.logger.debug(f"检查XLS文件格式失败 {file_path}: {e}")
            return False
