#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试旧版Office文件支持
"""

import os
import sys
from pathlib import Path
from document_processor import DocumentProcessor

def test_legacy_file_support():
    """测试旧版Office文件支持"""
    print("=" * 60)
    print("测试旧版Office文件支持")
    print("=" * 60)

    # 初始化文档处理器
    processor = DocumentProcessor("./test_output_legacy")

    # 测试目录
    test_dir = Path("test_legacy_documents")
    if not test_dir.exists():
        print("❌ 测试目录不存在，请先运行 create_legacy_test_files.py")
        return False

    # 获取所有测试文件
    files = processor.get_files_in_directory(str(test_dir))
    if not files:
        print("❌ 未找到测试文件")
        return False

    print(f"找到 {len(files)} 个旧版Office文件")
    print()

    success_count = 0
    total_count = len(files)

    for file_path in files:
        file_name = Path(file_path).name
        extension = Path(file_path).suffix.lower()

        print(f"📄 测试文件: {file_name}")
        print(f"   文件类型: {extension}")

        try:
            # 计算哈希值
            file_hash = processor.calculate_file_hash(file_path)
            if file_hash:
                print(f"   ✓ 哈希值: {file_hash[:16]}...")
            else:
                print(f"   ❌ 哈希值计算失败")
                continue

            # 提取文档内容
            doc_info = processor.extract_document_content(file_path)

            # 检查内容提取
            content = doc_info['content']
            if content:
                content_preview = content[:100].replace('\n', ' ').replace('\r', ' ')
                print(f"   ✓ 内容提取成功 ({len(content)} 字符)")
                print(f"   📝 内容预览: {content_preview}...")

                # 检查中文内容
                chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
                if chinese_chars > 0:
                    print(f"   ✓ 中文字符检测: {chinese_chars} 个中文字符")
                else:
                    print(f"   ⚠️  未检测到中文字符")

            else:
                print(f"   ❌ 内容提取失败")
                continue

            # 检查创建者信息
            if doc_info['creator']:
                print(f"   ✓ 创建者: {doc_info['creator']}")
            else:
                print(f"   ℹ️  创建者信息: 未获取到")

            # 检查创建时间
            if doc_info['create_time']:
                print(f"   ✓ 创建时间: {doc_info['create_time']}")
            else:
                print(f"   ℹ️  创建时间: 未获取到")

            success_count += 1
            print(f"   ✅ 处理成功")

        except Exception as e:
            print(f"   ❌ 处理失败: {e}")

        print()

    # 输出测试结果
    print("=" * 60)
    print("测试结果汇总:")
    print(f"总文件数: {total_count}")
    print(f"成功处理: {success_count}")
    print(f"失败文件: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")

    if success_count == total_count:
        print("\n🎉 所有旧版Office文件处理成功！")
        return True
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个文件处理失败")
        return False

def test_individual_extractors():
    """测试各个提取器的功能"""
    print("\n" + "=" * 60)
    print("测试各个提取器功能")
    print("=" * 60)

    processor = DocumentProcessor("./test_output_legacy")
    test_dir = Path("test_legacy_documents")

    # 测试DOC文件提取器
    doc_files = list(test_dir.glob("*.doc"))
    if doc_files:
        doc_file = str(doc_files[0])
        print(f"\n📄 测试DOC文件: {Path(doc_file).name}")

        # 测试olefile提取器
        print("   测试olefile提取器...")
        content, creator, create_time = processor.extract_doc_content_with_olefile(doc_file)
        if content:
            print(f"   ✓ olefile提取成功: {len(content)} 字符")
            print(f"     预览: {content[:50]}...")
        else:
            print(f"   ❌ olefile提取失败")

        # 测试antiword提取器
        print("   测试antiword提取器...")
        content = processor.extract_with_antiword(doc_file)
        if content:
            print(f"   ✓ antiword提取成功: {len(content)} 字符")
            print(f"     预览: {content[:50]}...")
        else:
            print(f"   ❌ antiword提取失败")

        # 测试catdoc提取器
        print("   测试catdoc提取器...")
        content = processor.extract_with_catdoc(doc_file)
        if content:
            print(f"   ✓ catdoc提取成功: {len(content)} 字符")
            print(f"     预览: {content[:50]}...")
        else:
            print(f"   ❌ catdoc提取失败")

    # 测试XLS文件提取器
    xls_files = list(test_dir.glob("*.xls"))
    if xls_files:
        xls_file = str(xls_files[0])
        print(f"\n📊 测试XLS文件: {Path(xls_file).name}")

        # 测试olefile提取器
        print("   测试olefile提取器...")
        content, creator, create_time = processor.extract_xls_content_with_olefile(xls_file)
        if content:
            print(f"   ✓ olefile提取成功: {len(content)} 字符")
            print(f"     预览: {content[:50]}...")
        else:
            print(f"   ❌ olefile提取失败")

        # 测试pandas提取器
        print("   测试pandas提取器...")
        content = processor.extract_xls_with_pandas(xls_file)
        if content:
            print(f"   ✓ pandas提取成功: {len(content)} 字符")
            print(f"     预览: {content[:50]}...")
        else:
            print(f"   ❌ pandas提取失败")

    # 测试LibreOffice提取器
    print(f"\n🖥️  测试LibreOffice提取器...")
    all_files = list(test_dir.glob("*.*"))
    for file_path in all_files:
        if file_path.suffix.lower() in {'.doc', '.xls', '.ppt'}:
            print(f"   测试文件: {file_path.name}")
            content = processor.extract_with_libreoffice(str(file_path))
            if content:
                print(f"   ✓ LibreOffice提取成功: {len(content)} 字符")
                print(f"     预览: {content[:50]}...")
            else:
                print(f"   ❌ LibreOffice提取失败")

def main():
    """主函数"""
    print("旧版Office文件支持测试")
    print("=" * 60)

    # 基本功能测试
    basic_success = test_legacy_file_support()

    # 详细提取器测试
    test_individual_extractors()

    print("\n" + "=" * 60)
    if basic_success:
        print("🎉 旧版Office文件支持测试通过！")
        print("\n现在可以使用增强版系统处理旧版Office文件:")
        print("python3 document_manager.py test_legacy_documents")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
    print("=" * 60)

if __name__ == "__main__":
    main()
