#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建旧版Office文件用于测试
"""

import os
import subprocess
import tempfile
from pathlib import Path

def create_legacy_files():
    """创建旧版Office测试文件"""
    test_dir = Path("test_legacy_documents")
    test_dir.mkdir(exist_ok=True)
    
    print("正在创建旧版Office测试文件...")
    
    # 使用LibreOffice创建旧版格式文件
    try:
        # 创建临时的新格式文件
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir = Path(temp_dir)
            
            # 创建临时docx文件
            temp_docx = temp_dir / "temp.docx"
            from docx import Document
            doc = Document()
            doc.add_heading('旧版DOC测试文档', 0)
            doc.add_paragraph('这是一个用于测试旧版DOC文件提取功能的文档。')
            doc.add_paragraph('包含中文内容，用于验证编码处理。')
            doc.add_paragraph('第三段落，测试多段落提取。')
            doc.save(temp_docx)
            
            # 转换为DOC格式
            doc_file = test_dir / "legacy_test_document.doc"
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'doc',
                '--outdir', str(test_dir),
                str(temp_docx)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                # 重命名文件
                converted_file = test_dir / "temp.doc"
                if converted_file.exists():
                    converted_file.rename(doc_file)
                    print(f"✓ 创建了 {doc_file.name}")
                else:
                    print(f"✗ DOC文件转换失败")
            else:
                print(f"✗ DOC文件转换失败: {result.stderr}")
                
            # 创建临时xlsx文件
            temp_xlsx = temp_dir / "temp.xlsx"
            from openpyxl import Workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "旧版测试工作表"
            ws['A1'] = '产品名称'
            ws['B1'] = '价格'
            ws['C1'] = '库存'
            ws['A2'] = '苹果'
            ws['B2'] = 5.5
            ws['C2'] = 100
            ws['A3'] = '香蕉'
            ws['B3'] = 3.2
            ws['C3'] = 80
            ws['A4'] = '橙子'
            ws['B4'] = 4.8
            ws['C4'] = 60
            wb.save(temp_xlsx)
            
            # 转换为XLS格式
            xls_file = test_dir / "legacy_test_spreadsheet.xls"
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'xls',
                '--outdir', str(test_dir),
                str(temp_xlsx)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                # 重命名文件
                converted_file = test_dir / "temp.xls"
                if converted_file.exists():
                    converted_file.rename(xls_file)
                    print(f"✓ 创建了 {xls_file.name}")
                else:
                    print(f"✗ XLS文件转换失败")
            else:
                print(f"✗ XLS文件转换失败: {result.stderr}")
                
            # 创建临时pptx文件
            temp_pptx = temp_dir / "temp.pptx"
            from pptx import Presentation
            prs = Presentation()
            
            # 添加标题幻灯片
            title_slide_layout = prs.slide_layouts[0]
            slide = prs.slides.add_slide(title_slide_layout)
            title = slide.shapes.title
            subtitle = slide.placeholders[1]
            title.text = "旧版PPT测试演示"
            subtitle.text = "用于测试旧版PowerPoint文件处理"
            
            # 添加内容幻灯片
            bullet_slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(bullet_slide_layout)
            shapes = slide.shapes
            title_shape = shapes.title
            body_shape = shapes.placeholders[1]
            
            title_shape.text = '测试内容'
            tf = body_shape.text_frame
            tf.text = '第一个测试要点'
            
            p = tf.add_paragraph()
            p.text = '第二个测试要点'
            p.level = 0
            
            p = tf.add_paragraph()
            p.text = '第三个测试要点'
            p.level = 0
            
            prs.save(temp_pptx)
            
            # 转换为PPT格式
            ppt_file = test_dir / "legacy_test_presentation.ppt"
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'ppt',
                '--outdir', str(test_dir),
                str(temp_pptx)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                # 重命名文件
                converted_file = test_dir / "temp.ppt"
                if converted_file.exists():
                    converted_file.rename(ppt_file)
                    print(f"✓ 创建了 {ppt_file.name}")
                else:
                    print(f"✗ PPT文件转换失败")
            else:
                print(f"✗ PPT文件转换失败: {result.stderr}")
                
    except Exception as e:
        print(f"创建旧版文件时出错: {e}")
        
    print(f"\n旧版测试文件已创建在 {test_dir} 目录中")
    return str(test_dir)

if __name__ == "__main__":
    create_legacy_files()
