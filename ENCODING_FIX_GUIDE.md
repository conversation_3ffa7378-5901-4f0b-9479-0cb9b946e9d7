# 数据库编码问题修复指南

## 问题描述

数据库中的中文内容出现乱码，主要表现为：
- 中文字符显示为问号或其他乱码
- 文档内容无法正确显示
- 文件名编码错误

## 解决方案概述

本解决方案通过以下几个方面来修复编码问题：

1. **数据库连接优化**：添加UTF-8编码支持
2. **文本规范化**：统一文本编码处理
3. **多编码支持**：支持多种编码格式的文档
4. **数据修复**：修复已存在的乱码数据

## 修复步骤

### 步骤1：测试当前编码配置

首先运行编码测试脚本，检查当前系统的编码处理能力：

```bash
python3 test_encoding.py
```

这个脚本会：
- 测试数据库连接的编码配置
- 测试中文字符的插入和读取
- 检查现有数据的编码情况
- 验证文本规范化功能

### 步骤2：检测编码问题

运行编码问题检测脚本：

```bash
python3 fix_encoding_issues.py
```

这个脚本会：
- 扫描数据库中的所有记录
- 识别有编码问题的记录
- 提供详细的问题报告

### 步骤3：备份数据库

在修复之前，强烈建议备份数据库：

```sql
-- 在SQL Server中执行
SELECT * INTO documents_backup FROM documents;
```

或者使用修复脚本的自动备份功能。

### 步骤4：执行修复

运行修复脚本并按照提示操作：

```bash
python3 fix_encoding_issues.py
```

修复过程包括：
1. 检测编码问题
2. 创建数据库备份
3. 试运行修复（预览修复结果）
4. 执行实际修复

### 步骤5：验证修复结果

修复完成后，再次运行测试脚本验证：

```bash
python3 test_encoding.py
```

## 技术改进详情

### 1. 数据库连接改进

**修改文件**: `config_loader.py`

添加了UTF-8字符集支持：
```python
conn_str = (
    f"DRIVER={self.config['driver']};"
    f"SERVER={self.config['server']};"
    f"DATABASE={self.config['database']};"
    f"UID={self.config['uid']};"
    f"PWD={self.config['pwd']};"
    f"Encrypt={self.config['encrypt']};"
    f"TrustServerCertificate={self.config['trustServerCertificate']};"
    f"CharacterSet=UTF-8;"
    f"ApplicationIntent=ReadWrite;"
)
```

### 2. 数据库处理器改进

**修改文件**: `database_handler.py`

- 添加了连接编码设置
- 实现了文本规范化功能
- 改进了数据插入时的编码处理

关键改进：
```python
# 设置连接编码
self.connection.setdecoding(pyodbc.SQL_CHAR, encoding='utf-8')
self.connection.setdecoding(pyodbc.SQL_WCHAR, encoding='utf-8')
self.connection.setencoding(encoding='utf-8')

# 文本规范化
def normalize_text(self, text: str) -> str:
    # Unicode规范化
    text = unicodedata.normalize('NFC', text)
    # 移除控制字符
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    # 清理空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    return text
```

### 3. 文档处理器改进

**修改文件**: `document_processor.py`

改进了LibreOffice文本提取的编码处理：
```python
# 尝试多种编码方式读取文件
for encoding in ['utf-8', 'utf-16', 'gbk', 'gb2312', 'cp1252']:
    try:
        with open(txt_files[0], 'r', encoding=encoding) as f:
            content = f.read()
        if content.strip():
            break
    except (UnicodeDecodeError, UnicodeError):
        continue
```

## 预防措施

为了避免将来出现编码问题：

1. **统一编码标准**：所有文本处理都使用UTF-8编码
2. **输入验证**：在数据插入前进行编码规范化
3. **定期检查**：定期运行编码测试脚本
4. **文档标准**：建立文档编码处理标准

## 常见问题解答

### Q: 修复后还是有乱码怎么办？

A: 可能的原因和解决方法：
1. 检查原始文档的编码格式
2. 确认数据库服务器的字符集设置
3. 验证ODBC驱动版本是否支持UTF-8

### Q: 修复过程中出现错误怎么办？

A: 
1. 检查数据库连接是否正常
2. 确认有足够的数据库权限
3. 查看详细的错误日志
4. 如有必要，从备份恢复数据

### Q: 如何处理特殊格式的文档？

A: 
1. 确保安装了相应的文档处理库
2. 检查LibreOffice是否正确安装
3. 对于特殊格式，可能需要专门的处理方法

## 监控和维护

建议定期执行以下维护任务：

1. **每周运行编码测试**：
   ```bash
   python3 test_encoding.py
   ```

2. **每月检查数据质量**：
   ```bash
   python3 fix_encoding_issues.py
   ```

3. **备份重要数据**：
   定期备份documents表

## 联系支持

如果遇到问题，请：
1. 查看日志文件：`encoding_fix.log`
2. 收集错误信息和系统环境
3. 联系技术支持团队
