#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试XLS处理器的编码处理能力
"""

import os
import sys
import logging
from pathlib import Path

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('xls_encoding_test.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_utf16_compatibility():
    """测试UTF-16兼容性"""
    print("🔧 测试UTF-16编码兼容性")
    print("=" * 60)
    
    try:
        from xls_processor import XLSProcessor
        
        processor = XLSProcessor()
        
        # 测试各种中文内容
        test_contents = [
            "产品名称,价格,数量\n苹果,5.5,100\n香蕉,3.2,80",
            "测试中文内容：这是一个包含中文的文档",
            "工作表: 销售数据\n产品 | 价格 | 库存\n苹果 | 5.5 | 100",
            "Microsoft Excel文档\n包含特殊字符：①②③④⑤",
            "混合内容：ABC中文123测试",
            "",  # 空内容
            "纯英文内容 without Chinese characters",
            "特殊符号：！@#￥%……&*（）——+",
        ]
        
        print(f"测试 {len(test_contents)} 种不同内容...")
        
        for i, content in enumerate(test_contents, 1):
            print(f"\n测试 {i}: {content[:30]}...")
            
            try:
                cleaned = processor.clean_and_encode_content(content)
                
                # 验证UTF-16兼容性
                try:
                    encoded = cleaned.encode('utf-16le')
                    decoded = encoded.decode('utf-16le')
                    
                    if decoded == cleaned:
                        print(f"   ✅ UTF-16兼容性: 通过")
                    else:
                        print(f"   ❌ UTF-16兼容性: 编码解码不一致")
                        
                    print(f"   📝 清理后长度: {len(cleaned)} 字符")
                    if cleaned:
                        preview = cleaned.replace('\n', ' ')[:50]
                        print(f"   📝 内容预览: {preview}...")
                        
                        # 统计中文字符
                        chinese_count = sum(1 for c in cleaned if '\u4e00' <= c <= '\u9fff')
                        if chinese_count > 0:
                            print(f"   🈶 中文字符: {chinese_count} 个")
                    else:
                        print(f"   ⚠️  清理后内容为空")
                        
                except UnicodeEncodeError as e:
                    print(f"   ❌ UTF-16编码失败: {e}")
                except UnicodeDecodeError as e:
                    print(f"   ❌ UTF-16解码失败: {e}")
                    
            except Exception as e:
                print(f"   ❌ 处理异常: {e}")
                
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_real_xls_files():
    """测试真实XLS文件的编码处理"""
    print(f"\n🔍 测试真实XLS文件编码处理")
    print("=" * 60)
    
    try:
        from xls_processor import XLSProcessor
        
        processor = XLSProcessor()
        
        # 查找测试文件
        test_files = []
        for test_dir in ["test_legacy_documents", "test_documents"]:
            if Path(test_dir).exists():
                for file_path in Path(test_dir).rglob("*.xls"):
                    test_files.append(str(file_path))
                    
        if not test_files:
            print("❌ 未找到XLS测试文件")
            return False
            
        print(f"找到 {len(test_files)} 个XLS文件")
        
        for file_path in test_files:
            file_name = Path(file_path).name
            print(f"\n📊 测试文件: {file_name}")
            
            try:
                # 测试各种提取方法
                methods = [
                    ("LibreOffice CSV", processor.extract_with_libreoffice_csv),
                    ("strings命令", processor.extract_with_strings),
                    ("hexdump分析", processor.extract_with_hexdump)
                ]
                
                for method_name, method_func in methods:
                    print(f"   🔧 测试方法: {method_name}")
                    
                    try:
                        content = method_func(file_path)
                        
                        if content:
                            # 验证UTF-16兼容性
                            try:
                                encoded = content.encode('utf-16le')
                                decoded = encoded.decode('utf-16le')
                                
                                if decoded == content:
                                    print(f"      ✅ UTF-16兼容: 通过")
                                else:
                                    print(f"      ❌ UTF-16兼容: 失败")
                                    
                                print(f"      📝 提取长度: {len(content)} 字符")
                                
                                # 检查中文字符
                                chinese_count = sum(1 for c in content if '\u4e00' <= c <= '\u9fff')
                                if chinese_count > 0:
                                    print(f"      🈶 中文字符: {chinese_count} 个")
                                    
                                # 内容预览
                                preview = content.replace('\n', ' ')[:50]
                                print(f"      📝 内容预览: {preview}...")
                                
                            except (UnicodeEncodeError, UnicodeDecodeError) as e:
                                print(f"      ❌ 编码问题: {e}")
                                
                        else:
                            print(f"      ⚠️  未提取到内容")
                            
                    except Exception as e:
                        print(f"      ❌ 方法异常: {e}")
                        
                # 测试完整提取流程
                print(f"   🔄 测试完整提取流程...")
                content, creator, create_time = processor.extract_xls_content(file_path)
                
                if content:
                    try:
                        # 最终UTF-16验证
                        encoded = content.encode('utf-16le')
                        decoded = encoded.decode('utf-16le')
                        
                        if decoded == content:
                            print(f"   ✅ 最终结果UTF-16兼容: 通过")
                        else:
                            print(f"   ❌ 最终结果UTF-16兼容: 失败")
                            
                        print(f"   📊 最终内容长度: {len(content)} 字符")
                        chinese_count = sum(1 for c in content if '\u4e00' <= c <= '\u9fff')
                        if chinese_count > 0:
                            print(f"   🈶 最终中文字符: {chinese_count} 个")
                            
                    except (UnicodeEncodeError, UnicodeDecodeError) as e:
                        print(f"   ❌ 最终编码问题: {e}")
                else:
                    print(f"   ⚠️  最终未提取到内容")
                    
            except Exception as e:
                print(f"   ❌ 文件处理异常: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 XLS编码处理测试")
    print("=" * 60)
    
    setup_test_logging()
    
    success_count = 0
    total_tests = 2
    
    # 测试1: UTF-16兼容性
    if test_utf16_compatibility():
        success_count += 1
        
    # 测试2: 真实文件编码处理
    if test_real_xls_files():
        success_count += 1
        
    # 结果汇总
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果汇总")
    print(f"=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！XLS编码处理正常。")
        print(f"\n✨ 修复效果:")
        print(f"✅ UTF-16编码兼容性完善")
        print(f"✅ 中文字符处理优化")
        print(f"✅ 多种编码自动检测")
        print(f"✅ 字符清理和验证机制")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步优化")
        
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
