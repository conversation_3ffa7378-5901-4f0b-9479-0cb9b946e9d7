#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库编码配置
"""

import logging
import sys
from database_handler import DatabaseHandler
from config_loader import ConfigLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


def test_database_encoding():
    """测试数据库编码配置"""
    
    print("=== 数据库编码测试 ===")
    print()
    
    # 初始化数据库处理器
    config_loader = ConfigLoader()
    db_handler = DatabaseHandler(config_loader)
    
    try:
        # 连接数据库
        print("1. 测试数据库连接...")
        if not db_handler.connect():
            print("❌ 数据库连接失败")
            return False
        print("✅ 数据库连接成功")
        
        # 测试中文字符插入和读取
        print("2. 测试中文字符处理...")
        
        test_texts = [
            "测试中文字符：你好世界！",
            "特殊字符：①②③④⑤⑥⑦⑧⑨⑩",
            "数学符号：∑∏∫∮∇∂∆∞≠≤≥±×÷",
            "日文假名：ひらがな カタカナ",
            "韩文：안녕하세요",
            "表格数据：江苏省 | 南京市 | 玄武区 | 240 | 120 | 82.5%",
            "混合内容：2022年江苏省统计数据(截至11.15)\n省份 | 城市 | 人口数量 | 经济指标"
        ]
        
        for i, test_text in enumerate(test_texts):
            try:
                # 测试文本规范化
                normalized_text = db_handler.normalize_text(test_text)
                
                print(f"  测试 {i+1}: {test_text[:30]}...")
                print(f"    原始长度: {len(test_text)}")
                print(f"    规范化长度: {len(normalized_text)}")
                print(f"    规范化结果: {normalized_text[:50]}...")
                
                # 测试数据库插入（使用临时表）
                test_sql = """
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='encoding_test' AND xtype='U')
                CREATE TABLE encoding_test (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    test_content NTEXT
                )
                """
                db_handler.cursor.execute(test_sql)
                
                # 插入测试数据
                insert_sql = "INSERT INTO encoding_test (test_content) VALUES (?)"
                db_handler.cursor.execute(insert_sql, (normalized_text,))
                db_handler.connection.commit()
                
                # 读取测试数据
                select_sql = "SELECT TOP 1 test_content FROM encoding_test ORDER BY id DESC"
                db_handler.cursor.execute(select_sql)
                result = db_handler.cursor.fetchone()
                
                if result and result[0] == normalized_text:
                    print(f"    ✅ 插入和读取成功")
                else:
                    print(f"    ❌ 插入或读取失败")
                    print(f"    期望: {normalized_text[:50]}...")
                    print(f"    实际: {result[0][:50] if result and result[0] else 'None'}...")
                
            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
        
        # 清理测试表
        try:
            db_handler.cursor.execute("DROP TABLE IF EXISTS encoding_test")
            db_handler.connection.commit()
        except:
            pass
        
        print()
        print("3. 测试现有数据...")
        
        # 检查现有数据的编码情况
        try:
            sample_sql = "SELECT TOP 5 id, original_filename, LEFT(document_content, 100) as content_sample FROM documents"
            db_handler.cursor.execute(sample_sql)
            samples = db_handler.cursor.fetchall()
            
            if samples:
                print(f"  找到 {len(samples)} 条样本数据:")
                for sample in samples:
                    doc_id, filename, content_sample = sample
                    print(f"    ID {doc_id}: {filename}")
                    if content_sample:
                        print(f"      内容样本: {content_sample[:50]}...")
                    else:
                        print(f"      内容样本: [空]")
            else:
                print("  未找到现有数据")
                
        except Exception as e:
            print(f"  检查现有数据失败: {e}")
        
        print()
        print("✅ 编码测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        db_handler.disconnect()


def test_text_normalization():
    """测试文本规范化功能"""
    
    print("=== 文本规范化测试 ===")
    print()
    
    db_handler = DatabaseHandler(ConfigLoader())
    
    test_cases = [
        # (输入, 描述)
        ("正常中文文本", "正常中文"),
        ("Mixed English 和中文", "中英混合"),
        ("包含\x00控制\x01字符\x02的文本", "包含控制字符"),
        ("多个    空格   的    文本", "多余空格"),
        ("换行符\n和\t制表符", "换行和制表符"),
        ("特殊符号：①②③④⑤", "特殊符号"),
        (b'\xe4\xb8\xad\xe6\x96\x87'.decode('utf-8'), "UTF-8字节"),
        ("", "空字符串"),
        (None, "None值"),
    ]
    
    for i, (test_input, description) in enumerate(test_cases):
        try:
            print(f"测试 {i+1}: {description}")
            print(f"  输入: {repr(test_input)}")
            
            result = db_handler.normalize_text(test_input)
            print(f"  输出: {repr(result)}")
            print(f"  长度: {len(result) if result else 0}")
            print()
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            print()


if __name__ == "__main__":
    print("开始编码测试...")
    print()
    
    # 测试文本规范化
    test_text_normalization()
    
    # 测试数据库编码
    test_database_encoding()
    
    print("测试完成！")
