#!/bin/bash

echo "正在安装文档保存系统依赖..."

# 更新包管理器
echo "更新包管理器..."
sudo apt update

# 安装系统依赖
echo "安装系统依赖..."
sudo apt install -y python3-pip python3-dev unixodbc-dev

# 安装旧版Office文件处理工具
echo "安装旧版Office文件处理工具..."
sudo apt install -y antiword catdoc

# 注意: xlhtml在Ubuntu 22.04中不可用，我们将使用Python的olefile库作为替代

# 安装Microsoft ODBC Driver 18 for SQL Server (如果尚未安装)
echo "检查并安装Microsoft ODBC Driver 18..."
if ! odbcinst -q -d -n "ODBC Driver 18 for SQL Server" > /dev/null 2>&1; then
    echo "安装Microsoft ODBC Driver 18 for SQL Server..."
    curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
    curl https://packages.microsoft.com/config/ubuntu/22.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
    sudo apt update
    sudo ACCEPT_EULA=Y apt install -y msodbcsql18
else
    echo "Microsoft ODBC Driver 18 已安装"
fi

# 安装Python依赖
echo "安装Python依赖..."
pip3 install -r requirements.txt

# 创建输出目录
echo "创建输出目录..."
sudo mkdir -p /data/save_doc
sudo chown $USER:$USER /data/save_doc

echo "依赖安装完成！"
echo ""
echo "使用方法："
echo "python3 document_manager.py <输入目录> [-o <输出目录>]"
echo ""
echo "示例："
echo "python3 document_manager.py ./documents"
echo "python3 document_manager.py ./documents -o /custom/output/path"
