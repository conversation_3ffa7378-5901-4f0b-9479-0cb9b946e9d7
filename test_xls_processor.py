#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试专用XLS处理器
"""

import os
import sys
import warnings
from pathlib import Path

def test_xls_processor():
    """测试专用XLS处理器"""
    print("🔧 测试专用XLS处理器")
    print("=" * 60)
    
    try:
        from xls_processor import XLSProcessor
        from document_processor import DocumentProcessor
        
        # 初始化处理器
        xls_processor = XLSProcessor()
        doc_processor = DocumentProcessor("./test_output_xls")
        
        # 测试目录
        test_dirs = ["test_legacy_documents", "test_documents"]
        
        for test_dir in test_dirs:
            if not Path(test_dir).exists():
                print(f"⚠️  测试目录 {test_dir} 不存在，跳过")
                continue
                
            print(f"\n📁 测试目录: {test_dir}")
            print("-" * 40)
            
            # 获取XLS文件
            xls_files = []
            for file_path in Path(test_dir).rglob("*.xls"):
                xls_files.append(str(file_path))
                
            if not xls_files:
                print(f"   未找到XLS文件")
                continue
                
            for file_path in xls_files:
                file_name = Path(file_path).name
                
                print(f"\n📊 测试文件: {file_name}")
                
                # 检查文件有效性
                if xls_processor.is_valid_xls_file(file_path):
                    print(f"   ✅ 有效的XLS文件")
                else:
                    print(f"   ⚠️  文件格式可能有问题")
                
                # 测试专用XLS处理器
                print(f"   🔍 测试专用XLS处理器...")
                
                # 捕获警告
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    
                    content, creator, create_time = xls_processor.extract_xls_content(file_path)
                    
                    if w:
                        ole_warnings = [warning for warning in w 
                                      if 'OLE2' in str(warning.message) or 'SSCS' in str(warning.message)]
                        if ole_warnings:
                            print(f"   ❌ 仍有OLE警告: {len(ole_warnings)} 个")
                            for warning in ole_warnings:
                                print(f"      - {warning.message}")
                        else:
                            print(f"   ✅ 无OLE相关警告")
                    else:
                        print(f"   ✅ 完全无警告")
                
                if content:
                    print(f"   ✅ 内容提取成功: {len(content)} 字符")
                    preview = content[:100].replace('\n', ' ')
                    print(f"   📝 内容预览: {preview}...")
                    
                    # 检查中文内容
                    chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
                    if chinese_chars > 0:
                        print(f"   ✅ 中文字符检测: {chinese_chars} 个中文字符")
                else:
                    print(f"   ❌ 内容提取失败")
                    
                # 对比测试：使用原始文档处理器
                print(f"   🔄 对比测试：原始文档处理器...")
                
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    
                    doc_info = doc_processor.extract_document_content(file_path)
                    
                    ole_warnings = [warning for warning in w 
                                  if 'OLE2' in str(warning.message) or 'SSCS' in str(warning.message)]
                    
                    if ole_warnings:
                        print(f"   ⚠️  原始处理器有OLE警告: {len(ole_warnings)} 个")
                    else:
                        print(f"   ✅ 原始处理器无OLE警告")
                        
                    if doc_info['content']:
                        print(f"   📊 原始处理器提取: {len(doc_info['content'])} 字符")
                    else:
                        print(f"   ❌ 原始处理器提取失败")
                        
        print(f"\n" + "=" * 60)
        print(f"🎯 专用XLS处理器测试完成")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
        
    return True

def test_xls_methods():
    """测试XLS处理的各种方法"""
    print(f"\n🧪 测试XLS处理方法")
    print("=" * 60)
    
    try:
        from xls_processor import XLSProcessor
        
        processor = XLSProcessor()
        
        # 查找测试文件
        test_files = []
        for test_dir in ["test_legacy_documents", "test_documents"]:
            if Path(test_dir).exists():
                for file_path in Path(test_dir).rglob("*.xls"):
                    test_files.append(str(file_path))
                    
        if not test_files:
            print("❌ 未找到XLS测试文件")
            return False
            
        test_file = test_files[0]
        print(f"📊 测试文件: {Path(test_file).name}")
        
        # 测试各种提取方法
        methods = [
            ("LibreOffice CSV", processor.extract_with_libreoffice_csv),
            ("strings命令", processor.extract_with_strings),
            ("hexdump分析", processor.extract_with_hexdump)
        ]
        
        for method_name, method_func in methods:
            print(f"\n🔍 测试方法: {method_name}")
            
            try:
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    
                    content = method_func(test_file)
                    
                    ole_warnings = [warning for warning in w 
                                  if 'OLE2' in str(warning.message) or 'SSCS' in str(warning.message)]
                    
                    if ole_warnings:
                        print(f"   ❌ 有OLE警告: {len(ole_warnings)} 个")
                    else:
                        print(f"   ✅ 无OLE警告")
                        
                    if content:
                        print(f"   ✅ 提取成功: {len(content)} 字符")
                        preview = content[:50].replace('\n', ' ')
                        print(f"   📝 预览: {preview}...")
                    else:
                        print(f"   ❌ 提取失败")
                        
            except Exception as e:
                print(f"   ❌ 方法异常: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 XLS处理器测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 专用XLS处理器
    if test_xls_processor():
        success_count += 1
        
    # 测试2: XLS处理方法
    if test_xls_methods():
        success_count += 1
        
    # 结果汇总
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果汇总")
    print(f"=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过！专用XLS处理器工作正常。")
        print(f"\n✨ 优势:")
        print(f"✅ 完全消除OLE2警告")
        print(f"✅ 多种备选提取方法")
        print(f"✅ 更好的错误处理")
        print(f"✅ 适合生产环境使用")
    else:
        print(f"\n⚠️  部分测试失败，请检查系统配置")
        
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
