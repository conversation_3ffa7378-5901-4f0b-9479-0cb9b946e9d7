#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作模块
"""

import pyodbc
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from config_loader import ConfigLoader


class DatabaseHandler:
    """数据库操作处理器"""

    def __init__(self, config_loader: ConfigLoader):
        """
        初始化数据库处理器

        Args:
            config_loader: 配置加载器实例
        """
        self.config_loader = config_loader
        self.connection = None
        self.cursor = None

    def connect(self) -> bool:
        """
        连接数据库

        Returns:
            连接是否成功
        """
        try:
            conn_str = self.config_loader.get_connection_string()
            self.connection = pyodbc.connect(conn_str)
            self.cursor = self.connection.cursor()
            logging.info("数据库连接成功")
            return True
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            return False

    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logging.info("数据库连接已断开")

    def test_connection(self) -> bool:
        """
        测试数据库连接

        Returns:
            连接是否正常
        """
        try:
            if not self.connection:
                return False
            self.cursor.execute("SELECT 1")
            return True
        except Exception as e:
            logging.error(f"数据库连接测试失败: {e}")
            return False

    def create_documents_table(self) -> bool:
        """
        创建文档存储表

        Returns:
            创建是否成功
        """
        try:
            create_table_sql = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='documents' AND xtype='U')
            CREATE TABLE documents (
                id INT IDENTITY(1,1) PRIMARY KEY,
                document_hash NVARCHAR(64) UNIQUE NOT NULL,
                original_filename NVARCHAR(255) NOT NULL,
                saved_filename NVARCHAR(255) NOT NULL,
                document_content NTEXT,
                document_creator NVARCHAR(100),
                document_create_time DATETIME,
                upload_time DATETIME DEFAULT GETDATE(),
                uploader NVARCHAR(100) DEFAULT N'Zhang Pengfei',
                document_keywords NVARCHAR(500),
                document_title NVARCHAR(255),
                document_summary NTEXT,
                document_source NVARCHAR(255),
                document_type NVARCHAR(50),
                department NVARCHAR(100),
                work_specialty NVARCHAR(100),
                analysis_time DATETIME DEFAULT '1999-01-01 00:00:00'
            )
            """
            self.cursor.execute(create_table_sql)
            self.connection.commit()
            logging.info("文档表创建成功或已存在")
            return True
        except Exception as e:
            logging.error(f"创建文档表失败: {e}")
            return False

    def document_exists(self, document_hash: str) -> bool:
        """
        检查文档是否已存在

        Args:
            document_hash: 文档哈希值

        Returns:
            文档是否存在
        """
        try:
            sql = "SELECT COUNT(*) FROM documents WHERE document_hash = ?"
            self.cursor.execute(sql, (document_hash,))
            count = self.cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            logging.error(f"检查文档是否存在失败: {e}")
            return False

    def insert_document(self, doc_data: Dict[str, Any]) -> bool:
        """
        插入文档记录

        Args:
            doc_data: 文档数据字典

        Returns:
            插入是否成功
        """
        try:
            sql = """
            INSERT INTO documents (
                document_hash, original_filename, saved_filename,
                document_content, document_creator, document_create_time,
                upload_time, uploader
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            values = (
                doc_data['document_hash'],
                doc_data['original_filename'],
                doc_data['saved_filename'],
                doc_data['document_content'],
                doc_data.get('document_creator', ''),
                doc_data.get('document_create_time'),
                datetime.now(),
                'Zhang Pengfei'
            )

            self.cursor.execute(sql, values)
            self.connection.commit()
            logging.info(f"文档 {doc_data['original_filename']} 插入成功")
            return True
        except Exception as e:
            logging.error(f"插入文档失败: {e}")
            return False
