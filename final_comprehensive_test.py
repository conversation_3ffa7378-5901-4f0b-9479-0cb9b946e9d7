#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档保存系统综合测试
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ 成功")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"❌ 失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
                
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 超时")
        return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def check_files_exist():
    """检查必要文件是否存在"""
    print(f"\n{'='*60}")
    print(f"📁 检查系统文件")
    print(f"{'='*60}")
    
    required_files = [
        'document_manager.py',
        'database_handler.py', 
        'document_processor.py',
        'config_loader.py',
        'nbfwq.json',
        'requirements.txt'
    ]
    
    all_exist = True
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 文件不存在")
            all_exist = False
            
    return all_exist

def check_dependencies():
    """检查依赖是否安装"""
    print(f"\n{'='*60}")
    print(f"📦 检查依赖包")
    print(f"{'='*60}")
    
    # Python包
    python_packages = [
        'pyodbc', 'python-docx', 'openpyxl', 'python-pptx', 
        'tqdm', 'chardet', 'olefile', 'pandas'
    ]
    
    for package in python_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
    
    # 系统工具
    system_tools = ['antiword', 'catdoc', 'libreoffice']
    
    for tool in system_tools:
        result = subprocess.run(f"which {tool}", shell=True, capture_output=True)
        if result.returncode == 0:
            print(f"✅ {tool}")
        else:
            print(f"❌ {tool} - 未安装")

def main():
    """主测试函数"""
    print("🚀 文档保存系统综合测试")
    print("="*60)
    
    # 检查文件
    if not check_files_exist():
        print("\n❌ 系统文件检查失败，请确保所有文件都存在")
        return False
    
    # 检查依赖
    check_dependencies()
    
    # 测试步骤
    tests = [
        ("python3 test_system.py", "基础系统测试"),
        ("python3 create_legacy_test_files.py", "创建旧版测试文件"),
        ("python3 test_legacy_support.py", "旧版Office文件支持测试"),
        ("python3 document_manager.py test_legacy_documents", "处理旧版Office文件"),
        ("python3 document_manager.py test_documents", "处理新版Office文件（重复检测）"),
        ("python3 check_database_records.py", "检查数据库记录")
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for cmd, description in tests:
        if run_command(cmd, description):
            success_count += 1
    
    # 最终结果
    print(f"\n{'='*60}")
    print(f"📊 测试结果汇总")
    print(f"{'='*60}")
    print(f"总测试数: {total_count}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过！系统完全可用。")
        print(f"\n📋 系统功能验证:")
        print(f"✅ 数据库连接和表创建")
        print(f"✅ 新版Office文件处理 (docx, xlsx, pptx)")
        print(f"✅ 旧版Office文件处理 (doc, xls, ppt)")
        print(f"✅ 多重提取引擎 (olefile, antiword, catdoc, pandas)")
        print(f"✅ 智能编码处理 (UTF-16, UTF-8, GBK)")
        print(f"✅ 重复文档检测")
        print(f"✅ 元数据提取")
        print(f"✅ 进度显示和统计")
        print(f"✅ 错误处理和恢复")
        
        print(f"\n🚀 系统使用方法:")
        print(f"python3 document_manager.py <输入目录> [-o <输出目录>]")
        
        print(f"\n📖 详细文档:")
        print(f"- README.md - 基础使用说明")
        print(f"- USAGE_GUIDE.md - 详细使用指南")
        print(f"- LEGACY_OFFICE_ENHANCEMENT.md - 旧版Office支持说明")
        
        return True
    else:
        print(f"\n⚠️  部分测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
