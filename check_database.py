#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

from config_loader import ConfigLoader
from database_handler import DatabaseHandler

def check_table_structure():
    """检查表结构"""
    config_loader = ConfigLoader()
    db_handler = DatabaseHandler(config_loader)
    
    try:
        if db_handler.connect():
            print("数据库连接成功")
            
            # 检查表是否存在
            cursor = db_handler.cursor
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'documents'
            """)
            
            tables = cursor.fetchall()
            if tables:
                print("documents表存在")
                
                # 查看表结构
                cursor.execute("""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'documents'
                    ORDER BY ORDINAL_POSITION
                """)
                
                columns = cursor.fetchall()
                print("\n当前表结构:")
                print("-" * 60)
                for col in columns:
                    print(f"{col[0]:<25} {col[1]:<15} {col[2]:<10} {col[3] or ''}")
                    
                # 删除表重新创建
                print("\n正在删除旧表...")
                cursor.execute("DROP TABLE documents")
                db_handler.connection.commit()
                print("旧表已删除")
                
            else:
                print("documents表不存在")
                
            # 重新创建表
            print("正在创建新表...")
            if db_handler.create_documents_table():
                print("新表创建成功")
                
                # 再次查看表结构
                cursor.execute("""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'documents'
                    ORDER BY ORDINAL_POSITION
                """)
                
                columns = cursor.fetchall()
                print("\n新表结构:")
                print("-" * 60)
                for col in columns:
                    print(f"{col[0]:<25} {col[1]:<15} {col[2]:<10} {col[3] or ''}")
            else:
                print("新表创建失败")
                
        else:
            print("数据库连接失败")
            
    except Exception as e:
        print(f"检查过程中出错: {e}")
    finally:
        db_handler.disconnect()

if __name__ == "__main__":
    check_table_structure()
